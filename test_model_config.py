#!/usr/bin/env python3
"""
测试脚本：验证模型配置文件和基础功能
"""

import sys
import yaml
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "autogen_framework"))

def test_config_file_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载...")
    
    try:
        config_path = Path("config/model_config.yaml")
        
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ 配置文件加载成功")
        
        # 检查LLM_CONFIGS
        llm_configs = config.get('LLM_CONFIGS', {})
        print(f"✅ 找到 {len(llm_configs)} 个模型配置")
        
        # 显示模型列表
        for model_name in list(llm_configs.keys())[:5]:  # 只显示前5个
            model_config = llm_configs[model_name]
            provider = model_config.get('provider', 'N/A')
            model = model_config.get('config', {}).get('model', 'N/A')
            print(f"   - {model_name}: {provider} ({model})")
        
        # 检查defaults
        defaults = config.get('defaults', {})
        print(f"✅ 默认配置: {defaults}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def test_model_config_structure():
    """测试模型配置结构"""
    print("🧪 测试模型配置结构...")
    
    try:
        config_path = Path("config/model_config.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        llm_configs = config.get('LLM_CONFIGS', {})
        
        # 检查每个模型配置的结构
        required_fields = ['provider', 'config']
        valid_models = 0
        
        for model_name, model_config in llm_configs.items():
            # 检查必需字段
            missing_fields = [field for field in required_fields if field not in model_config]
            if missing_fields:
                print(f"⚠️ 模型 {model_name} 缺少字段: {missing_fields}")
                continue
            
            # 检查config结构
            config_section = model_config.get('config', {})
            if 'model' not in config_section:
                print(f"⚠️ 模型 {model_name} 的config缺少model字段")
                continue
            
            # 检查model_capabilities
            capabilities = config_section.get('model_capabilities', {})
            if not isinstance(capabilities, dict):
                print(f"⚠️ 模型 {model_name} 的model_capabilities不是字典")
                continue
            
            valid_models += 1
        
        print(f"✅ {valid_models}/{len(llm_configs)} 个模型配置结构正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型配置结构测试失败: {e}")
        return False

def test_model_capabilities():
    """测试模型能力配置"""
    print("🧪 测试模型能力配置...")
    
    try:
        config_path = Path("config/model_config.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        llm_configs = config.get('LLM_CONFIGS', {})
        
        # 统计能力
        capability_stats = {
            'vision': 0,
            'function_calling': 0,
            'json_output': 0
        }
        
        for model_name, model_config in llm_configs.items():
            capabilities = model_config.get('config', {}).get('model_capabilities', {})
            
            for cap in capability_stats:
                if capabilities.get(cap, False):
                    capability_stats[cap] += 1
        
        print(f"✅ 能力统计:")
        for cap, count in capability_stats.items():
            print(f"   - {cap}: {count} 个模型支持")
        
        # 找出支持特定能力的模型
        vision_models = []
        function_calling_models = []
        
        for model_name, model_config in llm_configs.items():
            capabilities = model_config.get('config', {}).get('model_capabilities', {})
            
            if capabilities.get('vision', False):
                vision_models.append(model_name)
            
            if capabilities.get('function_calling', False):
                function_calling_models.append(model_name)
        
        print(f"✅ 支持视觉的模型: {vision_models}")
        print(f"✅ 支持函数调用的模型: {function_calling_models[:3]}...")  # 只显示前3个
        
        return True
        
    except Exception as e:
        print(f"❌ 模型能力测试失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量配置"""
    print("🧪 测试环境变量配置...")
    
    try:
        config_path = Path("config/model_config.yaml")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        llm_configs = config.get('LLM_CONFIGS', {})
        
        # 查找环境变量引用
        env_vars_found = set()
        
        for model_name, model_config in llm_configs.items():
            config_section = model_config.get('config', {})
            
            for key, value in config_section.items():
                if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                    env_var = value[2:-1]
                    env_vars_found.add(env_var)
        
        print(f"✅ 找到环境变量引用: {sorted(env_vars_found)}")
        
        # 检查常见的环境变量
        common_env_vars = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'SILICONFLOW_API_KEY']
        for env_var in common_env_vars:
            if env_var in env_vars_found:
                print(f"   ✅ {env_var}: 已配置")
            else:
                print(f"   ⚠️ {env_var}: 未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境变量测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 测试AutoGen框架模型配置...")
    print("=" * 50)
    
    tests = [
        ("配置文件加载", test_config_file_loading),
        ("模型配置结构", test_model_config_structure),
        ("模型能力配置", test_model_capabilities),
        ("环境变量配置", test_environment_variables),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模型配置测试通过！")
        print("💡 提示：要测试完整功能，请安装AutoGen依赖包:")
        print("   pip install autogen-core autogen-agentchat autogen-ext[openai]")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
