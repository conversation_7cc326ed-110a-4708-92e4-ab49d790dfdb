# AutoGen Multi-Agent Framework - Model Configuration
# 使用AutoGen的load_component方法加载模型

# 模型配置 - 使用AutoGen标准格式
LLM_CONFIGS:
  # OpenAI Models
  gpt-4o:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: gpt-4o
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      model_capabilities:
        vision: true
        function_calling: true
        json_output: true

  gpt-4o-mini:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: gpt-4o-mini
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      model_capabilities:
        vision: false
        function_calling: true
        json_output: true

  gpt-3.5-turbo:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: gpt-3.5-turbo
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      model_capabilities:
        vision: false
        function_calling: true
        json_output: false

  # 通义千问模型 - SiliconFlow
  Qwen2.5-72B-Instruct:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: Qwen/Qwen2.5-72B-Instruct
      api_key: "${SILICONFLOW_API_KEY}"
      base_url: https://api.siliconflow.cn/v1
      model_capabilities:
        vision: false
        function_calling: true
        json_output: true

  # 内部模型 - ZTE
  Qwen2.5-32B-Instruct:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: Qwen2.5-32B-Instruct
      api_key: '123'
      base_url: http://10.239.212.61:5000/v1
      model_capabilities:
        vision: false
        function_calling: true
        json_output: true

  nebulacoder-v5.2:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: nebulacoder-v5.2
      api_key: '1234456'
      base_url: http://nebulacoder.dev.zte.com.cn:40081/v1
      model_capabilities:
        vision: false
        function_calling: true
        json_output: true

  nebulacoder-v6.0:
    provider: autogen_ext.models.openai.OpenAIChatCompletionClient
    config:
      model: nebulacoder-v6.0
      api_key: '1234456'
      base_url: http://nebulacoder.dev.zte.com.cn:40081/v1
      model_capabilities:
        vision: false
        function_calling: true
        json_output: true

  # Anthropic Claude
  claude-3-5-sonnet:
    provider: autogen_ext.models.anthropic.AnthropicChatCompletionClient
    config:
      model: claude-3-5-sonnet-20241022
      api_key: "${ANTHROPIC_API_KEY}"
      model_capabilities:
        vision: true
        function_calling: true
        json_output: true

  # Ollama本地模型
  llama3.1-8b:
    provider: autogen_ext.models.ollama.OllamaChatCompletionClient
    config:
      model: llama3.1:8b
      base_url: "http://localhost:11434"
      model_capabilities:
        vision: false
        function_calling: false
        json_output: false

# 默认模型配置
defaults:
  primary_model: "gpt-4o-mini"
  fallback_model: "gpt-3.5-turbo"
  local_model: "llama3.1-8b"
  coding_model: "nebulacoder-v6.0"
  reasoning_model: "Qwen2.5-72B-Instruct"
