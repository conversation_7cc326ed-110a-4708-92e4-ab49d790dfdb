# AutoGen Multi-Agent Framework - Model Configuration
# 支持多种模型提供商和配置

models:
  # OpenAI Models
  "gpt-4o":
    provider: "openai"
    model_name: "gpt-4o"
    config:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      temperature: 0.7
      max_tokens: 4096
      timeout: 60
    capabilities:
      - "text_generation"
      - "function_calling"
      - "vision"
      - "json_mode"
    cost_per_1k_tokens:
      input: 0.005
      output: 0.015
    context_window: 128000
    
  "gpt-4o-mini":
    provider: "openai"
    model_name: "gpt-4o-mini"
    config:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      temperature: 0.7
      max_tokens: 4096
      timeout: 60
    capabilities:
      - "text_generation"
      - "function_calling"
      - "json_mode"
    cost_per_1k_tokens:
      input: 0.00015
      output: 0.0006
    context_window: 128000
    
  "gpt-3.5-turbo":
    provider: "openai"
    model_name: "gpt-3.5-turbo"
    config:
      api_key: "${OPENAI_API_KEY}"
      base_url: "https://api.openai.com/v1"
      temperature: 0.7
      max_tokens: 4096
      timeout: 60
    capabilities:
      - "text_generation"
      - "function_calling"
    cost_per_1k_tokens:
      input: 0.0005
      output: 0.0015
    context_window: 16385

  # Azure OpenAI Models
  "azure-gpt-4":
    provider: "azure_openai"
    model_name: "gpt-4"
    config:
      api_key: "${AZURE_OPENAI_API_KEY}"
      azure_endpoint: "${AZURE_OPENAI_ENDPOINT}"
      api_version: "2024-02-15-preview"
      azure_deployment: "${AZURE_OPENAI_DEPLOYMENT_NAME}"
      temperature: 0.7
      max_tokens: 4096
      timeout: 60
    capabilities:
      - "text_generation"
      - "function_calling"
    cost_per_1k_tokens:
      input: 0.03
      output: 0.06
    context_window: 8192

  # Anthropic Claude Models
  "claude-3-5-sonnet":
    provider: "anthropic"
    model_name: "claude-3-5-sonnet-20241022"
    config:
      api_key: "${ANTHROPIC_API_KEY}"
      base_url: "https://api.anthropic.com"
      max_tokens: 4096
      timeout: 60
    capabilities:
      - "text_generation"
      - "function_calling"
      - "vision"
    cost_per_1k_tokens:
      input: 0.003
      output: 0.015
    context_window: 200000

  # Local/Ollama Models
  "llama3.1-8b":
    provider: "ollama"
    model_name: "llama3.1:8b"
    config:
      base_url: "http://localhost:11434"
      timeout: 120
    capabilities:
      - "text_generation"
    cost_per_1k_tokens:
      input: 0.0
      output: 0.0
    context_window: 128000

  "qwen2.5-32b":
    provider: "ollama"
    model_name: "qwen2.5:32b"
    config:
      base_url: "http://localhost:11434"
      timeout: 120
    capabilities:
      - "text_generation"
      - "function_calling"
    cost_per_1k_tokens:
      input: 0.0
      output: 0.0
    context_window: 32768

# 默认模型配置
defaults:
  primary_model: "gpt-4o-mini"
  fallback_model: "gpt-3.5-turbo"
  local_model: "llama3.1-8b"
  
# 模型提供商配置
providers:
  openai:
    client_class: "autogen_ext.models.OpenAIChatCompletionClient"
    required_env_vars:
      - "OPENAI_API_KEY"
    optional_env_vars:
      - "OPENAI_BASE_URL"
      
  azure_openai:
    client_class: "autogen_ext.models.AzureOpenAIChatCompletionClient"
    required_env_vars:
      - "AZURE_OPENAI_API_KEY"
      - "AZURE_OPENAI_ENDPOINT"
      - "AZURE_OPENAI_DEPLOYMENT_NAME"
    optional_env_vars:
      - "AZURE_OPENAI_API_VERSION"
      
  anthropic:
    client_class: "autogen_ext.models.AnthropicChatCompletionClient"
    required_env_vars:
      - "ANTHROPIC_API_KEY"
    optional_env_vars:
      - "ANTHROPIC_BASE_URL"
      
  ollama:
    client_class: "autogen_ext.models.OllamaChatCompletionClient"
    required_env_vars: []
    optional_env_vars:
      - "OLLAMA_BASE_URL"

# 模型能力定义
capabilities:
  text_generation:
    description: "基础文本生成能力"
    
  function_calling:
    description: "支持函数调用和工具使用"
    
  vision:
    description: "支持图像理解和分析"
    
  json_mode:
    description: "支持结构化JSON输出"
    
  code_generation:
    description: "专门的代码生成能力"
    
  reasoning:
    description: "高级推理和分析能力"

# 使用策略
usage_policies:
  cost_optimization:
    enabled: true
    prefer_cheaper_models: true
    max_cost_per_request: 0.10
    
  performance_optimization:
    enabled: true
    cache_responses: true
    parallel_requests: true
    max_concurrent_requests: 5
    
  fallback_strategy:
    enabled: true
    max_retries: 3
    retry_delay: 1.0
    use_fallback_model: true
