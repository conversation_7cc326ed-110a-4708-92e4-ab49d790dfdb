#!/usr/bin/env python3
"""
测试脚本：验证requirements.txt中的依赖包是否兼容
"""

import sys
import importlib
from typing import List, <PERSON><PERSON>

def test_import(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """测试导入包"""
    try:
        if import_name:
            module = importlib.import_module(import_name)
        else:
            module = importlib.import_module(package_name)
        
        # 尝试获取版本信息
        version = getattr(module, '__version__', 'Unknown')
        return True, version
    except ImportError as e:
        return False, str(e)

def main():
    """主测试函数"""
    print("🔍 测试依赖包兼容性...")
    print(f"Python版本: {sys.version}")
    print("-" * 50)
    
    # 核心依赖测试
    core_packages = [
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("pydantic-settings", "pydantic_settings"),
    ]
    
    # AutoGen相关测试
    autogen_packages = [
        ("autogen-agentchat", "autogen_agentchat"),
        ("autogen-ext", "autogen_ext"),
    ]
    
    # 其他依赖测试
    other_packages = [
        ("jinja2", "jinja2"),
        ("pyyaml", "yaml"),
        ("python-dotenv", "dotenv"),
        ("httpx", "httpx"),
        ("aiohttp", "aiohttp"),
        ("rich", "rich"),
        ("structlog", "structlog"),
    ]
    
    all_packages = core_packages + autogen_packages + other_packages
    
    success_count = 0
    total_count = len(all_packages)
    
    for package_name, import_name in all_packages:
        success, result = test_import(package_name, import_name)
        status = "✅" if success else "❌"
        
        if success:
            print(f"{status} {package_name}: v{result}")
            success_count += 1
        else:
            print(f"{status} {package_name}: {result}")
    
    print("-" * 50)
    print(f"测试结果: {success_count}/{total_count} 包可用")
    
    if success_count == total_count:
        print("🎉 所有依赖包都兼容！")
        return 0
    else:
        print("⚠️  部分依赖包不可用，需要安装")
        return 1

if __name__ == "__main__":
    sys.exit(main())
