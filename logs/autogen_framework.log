2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable OPENAI_API_KEY not found for api_key
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable OPENAI_API_KEY not found for api_key
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable OPENAI_API_KEY not found for api_key
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable AZURE_OPENAI_API_KEY not found for api_key
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable AZURE_OPENAI_ENDPOINT not found for azure_endpoint
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable AZURE_OPENAI_DEPLOYMENT_NAME not found for azure_deployment
2025-07-01 20:29:21 - autogen_framework.model_manager - WARNING - Environment variable ANTHROPIC_API_KEY not found for api_key
2025-07-01 20:29:21 - autogen_framework.model_manager - INFO - Loaded 7 model configurations
2025-07-01 20:29:21 - autogen_framework.model_manager - INFO - Model manager initialized with 7 models
2025-07-01 20:29:21 - autogen_framework.model_manager - ERROR - Missing environment variables for gpt-4o-mini: ['OPENAI_API_KEY']
2025-07-01 20:29:21 - autogen_framework.model_manager - INFO - Model llama3.1-8b validation passed
