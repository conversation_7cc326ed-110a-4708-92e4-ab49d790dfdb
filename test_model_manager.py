#!/usr/bin/env python3
"""
测试脚本：验证模型管理器的实现
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "autogen_framework"))

def test_model_config_loading():
    """测试模型配置加载"""
    print("🧪 测试模型配置加载...")
    
    try:
        from managers.model_manager import ModelManager, get_model_manager
        
        # 测试模型管理器创建
        model_manager = get_model_manager()
        print(f"✅ 模型管理器创建成功")
        
        # 测试模型列表
        models = model_manager.list_models()
        print(f"✅ 加载了 {len(models)} 个模型配置")
        
        # 显示模型信息
        for model in models[:3]:  # 只显示前3个
            print(f"   - {model.alias}: {model.provider}/{model.model_name}")
            print(f"     能力: {[cap for cap in ['text_generation', 'function_calling', 'vision', 'json_mode'] if model.supports_capability(cap)]}")
            print(f"     上下文窗口: {model.context_window}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型配置加载测试失败: {e}")
        return False

def test_model_info_retrieval():
    """测试模型信息获取"""
    print("🧪 测试模型信息获取...")

    try:
        from managers.model_manager import get_model_manager, ModelNotFoundException

        model_manager = get_model_manager()

        # 测试获取存在的模型
        model_info = model_manager.get_model_info("gpt-4o-mini")
        print(f"✅ 获取模型信息成功: gpt-4o-mini")
        print(f"   提供商: {model_info.get('provider', 'N/A')}")
        print(f"   配置: {list(model_info.get('config', {}).keys())}")

        # 测试获取不存在的模型
        try:
            model_manager.get_model_info("non_existent_model")
            print("❌ 应该抛出ModelNotFoundException")
            return False
        except ModelNotFoundException:
            print("✅ 正确抛出ModelNotFoundException")

        return True

    except Exception as e:
        print(f"❌ 模型信息获取测试失败: {e}")
        return False

def test_model_filtering():
    """测试模型过滤"""
    print("🧪 测试模型过滤...")

    try:
        from managers.model_manager import get_model_manager

        model_manager = get_model_manager()

        # 按能力过滤
        function_calling_models = model_manager.filter_models_by_capability("function_calling")
        print(f"✅ 支持函数调用的模型数量: {len(function_calling_models)}")

        vision_models = model_manager.filter_models_by_capability("vision")
        print(f"✅ 支持视觉的模型数量: {len(vision_models)}")

        json_output_models = model_manager.filter_models_by_capability("json_output")
        print(f"✅ 支持JSON输出的模型数量: {len(json_output_models)}")

        # 显示一些具体的模型
        if function_calling_models:
            print(f"   支持函数调用的模型: {function_calling_models[:3]}")

        return True

    except Exception as e:
        print(f"❌ 模型过滤测试失败: {e}")
        return False

def test_default_models():
    """测试默认模型"""
    print("🧪 测试默认模型...")

    try:
        from managers.model_manager import get_model_manager

        model_manager = get_model_manager()

        # 测试默认模型
        primary_model = model_manager.get_default_model("primary_model")
        fallback_model = model_manager.get_default_model("fallback_model")
        local_model = model_manager.get_default_model("local_model")
        coding_model = model_manager.get_default_model("coding_model")

        print(f"✅ 主要模型: {primary_model}")
        print(f"✅ 备用模型: {fallback_model}")
        print(f"✅ 本地模型: {local_model}")
        print(f"✅ 编程模型: {coding_model}")

        # 验证默认模型存在
        for model_name in [primary_model, fallback_model, local_model, coding_model]:
            if model_manager.has_model(model_name):
                model_info = model_manager.get_model_info(model_name)
                provider = model_info.get('provider', 'N/A')
                print(f"   {model_name}: {provider}")
            else:
                print(f"   {model_name}: 模型不存在")

        return True

    except Exception as e:
        print(f"❌ 默认模型测试失败: {e}")
        return False

def test_model_validation():
    """测试模型验证"""
    print("🧪 测试模型验证...")

    try:
        from managers.model_manager import get_model_manager

        model_manager = get_model_manager()

        # 测试验证（不需要实际的API密钥）
        models_to_test = ["gpt-4o-mini", "llama3.1-8b", "nebulacoder-v6.0"]

        for model_name in models_to_test:
            if model_manager.has_model(model_name):
                is_valid = model_manager.validate_model(model_name)
                status = "✅" if is_valid else "⚠️"
                print(f"{status} 模型 {model_name} 验证: {'通过' if is_valid else '失败（可能缺少API密钥）'}")
            else:
                print(f"⚠️ 模型 {model_name} 不存在")

        return True

    except Exception as e:
        print(f"❌ 模型验证测试失败: {e}")
        return False

def test_model_capabilities():
    """测试模型能力"""
    print("🧪 测试模型能力...")

    try:
        from managers.model_manager import get_model_manager

        model_manager = get_model_manager()

        # 测试模型能力查询
        test_models = ["gpt-4o", "gpt-4o-mini", "nebulacoder-v6.0"]

        for model_name in test_models:
            if model_manager.has_model(model_name):
                capabilities = model_manager.get_model_capabilities(model_name)
                print(f"✅ {model_name} 能力:")
                for cap, supported in capabilities.items():
                    status = "✅" if supported else "❌"
                    print(f"   {status} {cap}: {supported}")

                # 测试特定能力检查
                has_vision = model_manager.supports_capability(model_name, "vision")
                has_function_calling = model_manager.supports_capability(model_name, "function_calling")
                print(f"   视觉能力: {has_vision}, 函数调用: {has_function_calling}")
            else:
                print(f"⚠️ 模型 {model_name} 不存在")

        return True

    except Exception as e:
        print(f"❌ 模型能力测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 测试AutoGen框架模型管理器...")
    print("=" * 50)
    
    tests = [
        ("模型配置加载", test_model_config_loading),
        ("模型信息获取", test_model_info_retrieval),
        ("模型过滤", test_model_filtering),
        ("默认模型", test_default_models),
        ("模型验证", test_model_validation),
        ("模型能力", test_model_capabilities),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模型管理器测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())
