"""
AutoGen Multi-Agent Framework - FastAPI Application Entry Point

This is the main entry point for the AutoGen Multi-Agent Framework.
It sets up the FastAPI application with all necessary routers and middleware.

Uses Microsoft's official autogen-agentchat package for multi-agent functionality.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

# Import routers (will be created in later phases)
# from routers.agent_router import router as agent_router
# from routers.team_router import router as team_router

# Import infrastructure
# from infrastructure.logger import setup_logging
# from infrastructure.exceptions import AutoGenFrameworkException

# Application metadata
APP_NAME = "AutoGen Multi-Agent Framework"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Enterprise-grade multi-agent system based on AutoGen and FastAPI"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    print(f"🚀 Starting {APP_NAME} v{APP_VERSION}")
    
    # Initialize logging
    # setup_logging()
    
    # Initialize managers (will be implemented in later phases)
    # await initialize_managers()
    
    print("✅ Application startup complete")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down application")
    # Cleanup resources if needed
    print("✅ Application shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": APP_NAME,
        "version": APP_VERSION,
        "message": "AutoGen Multi-Agent Framework is running"
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with basic information."""
    return {
        "app_name": APP_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION,
        "docs_url": "/docs",
        "health_check": "/health"
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled exceptions."""
    logging.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "type": type(exc).__name__
        }
    )


# Include routers (will be uncommented in later phases)
# app.include_router(agent_router, prefix="/autogen", tags=["agents"])
# app.include_router(team_router, prefix="/autogen", tags=["teams"])


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
