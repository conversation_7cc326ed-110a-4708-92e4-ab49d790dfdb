"""
AutoGen Multi-Agent Framework - Logging Configuration

This module configures AutoGen's built-in logging system for the framework.
Uses AutoGen's TRACE_LOGGER_NAME and EVENT_LOGGER_NAME for structured logging.
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

try:
    from autogen_core import TRACE_LOGGER_NAME, EVENT_LOGGER_NAME, ROOT_LOGGER_NAME
except ImportError:
    # Fallback for development/testing without autogen-core
    TRACE_LOGGER_NAME = "autogen.trace"
    EVENT_LOGGER_NAME = "autogen.event"
    ROOT_LOGGER_NAME = "autogen"

# Framework-specific logger names
FRAMEWORK_TRACE_LOGGER = f"{TRACE_LOGGER_NAME}.framework"
FRAMEWORK_EVENT_LOGGER = f"{EVENT_LOGGER_NAME}.framework"


class AutoGenLoggingConfig:
    """AutoGen日志配置管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化日志配置
        
        Args:
            config: 日志配置字典
        """
        self.config = config or self._get_default_config()
        self._setup_logging()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S",
            "file_logging": {
                "enabled": True,
                "directory": "logs",
                "filename": "autogen_framework.log",
                "max_bytes": 10 * 1024 * 1024,  # 10MB
                "backup_count": 5
            },
            "console_logging": {
                "enabled": True,
                "level": "INFO"
            },
            "trace_logging": {
                "enabled": True,
                "level": "DEBUG"
            },
            "event_logging": {
                "enabled": True,
                "level": "INFO"
            }
        }
    
    def _setup_logging(self):
        """设置日志系统"""
        # 基础配置
        logging.basicConfig(
            level=getattr(logging, self.config.get("level", "INFO")),
            format=self.config.get("format"),
            datefmt=self.config.get("date_format")
        )
        
        # 设置AutoGen trace日志
        if self.config.get("trace_logging", {}).get("enabled", True):
            self._setup_trace_logging()
        
        # 设置AutoGen event日志
        if self.config.get("event_logging", {}).get("enabled", True):
            self._setup_event_logging()
        
        # 设置文件日志
        if self.config.get("file_logging", {}).get("enabled", True):
            self._setup_file_logging()
    
    def _setup_trace_logging(self):
        """设置trace日志"""
        trace_logger = logging.getLogger(TRACE_LOGGER_NAME)
        
        # 清除现有handlers
        trace_logger.handlers.clear()
        
        # 添加控制台handler
        if self.config.get("console_logging", {}).get("enabled", True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(
                logging.Formatter(
                    fmt="[TRACE] %(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    datefmt=self.config.get("date_format")
                )
            )
            trace_logger.addHandler(console_handler)
        
        # 设置日志级别
        trace_level = self.config.get("trace_logging", {}).get("level", "DEBUG")
        trace_logger.setLevel(getattr(logging, trace_level))
        
        # 防止重复日志
        trace_logger.propagate = False
    
    def _setup_event_logging(self):
        """设置event日志"""
        event_logger = logging.getLogger(EVENT_LOGGER_NAME)
        
        # 清除现有handlers
        event_logger.handlers.clear()
        
        # 添加控制台handler
        if self.config.get("console_logging", {}).get("enabled", True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(
                logging.Formatter(
                    fmt="[EVENT] %(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    datefmt=self.config.get("date_format")
                )
            )
            event_logger.addHandler(console_handler)
        
        # 设置日志级别
        event_level = self.config.get("event_logging", {}).get("level", "INFO")
        event_logger.setLevel(getattr(logging, event_level))
        
        # 防止重复日志
        event_logger.propagate = False
    
    def _setup_file_logging(self):
        """设置文件日志"""
        file_config = self.config.get("file_logging", {})
        if not file_config.get("enabled", True):
            return
        
        # 创建日志目录
        log_dir = Path(file_config.get("directory", "logs"))
        log_dir.mkdir(exist_ok=True)
        
        # 设置文件handler
        from logging.handlers import RotatingFileHandler
        
        log_file = log_dir / file_config.get("filename", "autogen_framework.log")
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=file_config.get("max_bytes", 10 * 1024 * 1024),
            backupCount=file_config.get("backup_count", 5)
        )
        
        file_handler.setFormatter(
            logging.Formatter(
                fmt=self.config.get("format"),
                datefmt=self.config.get("date_format")
            )
        )
        
        # 添加到根日志器
        root_logger = logging.getLogger(ROOT_LOGGER_NAME)
        root_logger.addHandler(file_handler)
        
        # 添加到框架日志器
        framework_logger = logging.getLogger("autogen_framework")
        framework_logger.addHandler(file_handler)
    
    def get_trace_logger(self, module_name: str) -> logging.Logger:
        """获取trace日志器"""
        return logging.getLogger(f"{TRACE_LOGGER_NAME}.{module_name}")
    
    def get_event_logger(self, module_name: str) -> logging.Logger:
        """获取event日志器"""
        return logging.getLogger(f"{EVENT_LOGGER_NAME}.{module_name}")
    
    def get_framework_logger(self, module_name: str) -> logging.Logger:
        """获取框架日志器"""
        return logging.getLogger(f"autogen_framework.{module_name}")


# 全局日志配置实例
_logging_config: Optional[AutoGenLoggingConfig] = None


def setup_logging(config: Optional[Dict[str, Any]] = None) -> AutoGenLoggingConfig:
    """
    设置全局日志配置
    
    Args:
        config: 日志配置字典
        
    Returns:
        AutoGenLoggingConfig实例
    """
    global _logging_config
    _logging_config = AutoGenLoggingConfig(config)
    return _logging_config


def get_trace_logger(module_name: str) -> logging.Logger:
    """获取trace日志器"""
    if _logging_config is None:
        setup_logging()
    return _logging_config.get_trace_logger(module_name)


def get_event_logger(module_name: str) -> logging.Logger:
    """获取event日志器"""
    if _logging_config is None:
        setup_logging()
    return _logging_config.get_event_logger(module_name)


def get_framework_logger(module_name: str) -> logging.Logger:
    """获取框架日志器"""
    if _logging_config is None:
        setup_logging()
    return _logging_config.get_framework_logger(module_name)


# 便捷函数
def log_framework_event(message: str, level: str = "INFO", **kwargs):
    """记录框架事件"""
    logger = get_framework_logger("events")
    # 避免与LogRecord的保留字段冲突
    extra_data = {f"event_{k}": v for k, v in kwargs.items()}
    getattr(logger, level.lower())(message, extra=extra_data)


def log_agent_activity(agent_name: str, activity: str, **kwargs):
    """记录agent活动"""
    logger = get_event_logger("agents")
    logger.info(f"Agent {agent_name}: {activity}", extra=kwargs)


def log_team_activity(team_name: str, activity: str, **kwargs):
    """记录team活动"""
    logger = get_event_logger("teams")
    logger.info(f"Team {team_name}: {activity}", extra=kwargs)
