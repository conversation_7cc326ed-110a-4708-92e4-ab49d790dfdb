"""
AutoGen Multi-Agent Framework - Model Manager

This module manages model configurations using AutoGen's load_component method.
Provides unified access to different model providers through AutoGen extensions.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from functools import lru_cache

from autogen_core.models import ChatCompletionClient
from infrastructure.exceptions import (
    ModelException,
    ModelNotFoundException,
    ConfigurationException,
    ErrorCode
)
from infrastructure.logging_config import get_framework_logger

logger = get_framework_logger("model_manager")


class ModelManager:
    """简化的模型管理器，使用AutoGen的load_component方法"""

    def __init__(self, config_path: Optional[Path] = None):
        """
        初始化模型管理器

        Args:
            config_path: 模型配置文件路径
        """
        self.config_path = config_path or Path(__file__).parent.parent / "config" / "model_config.yaml"
        self._config_cache: Optional[Dict[str, Any]] = None
        self._client_cache: Dict[str, ChatCompletionClient] = {}

        logger.info(f"Model manager initialized with config: {self.config_path}")
    
    def _load_model_config(self) -> Dict[str, Any]:
        """加载模型配置"""
        if self._config_cache is not None:
            return self._config_cache

        try:
            if not self.config_path.exists():
                raise ConfigurationException(
                    f"Model config file not found: {self.config_path}",
                    config_path=str(self.config_path)
                )

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            self._config_cache = config
            llm_configs = config.get('LLM_CONFIGS', {})
            logger.info(f"Loaded {len(llm_configs)} model configurations")

            return config

        except Exception as e:
            raise ConfigurationException(f"Failed to load model config: {e}", cause=e)
    
    @property
    def model_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self._load_model_config().get('LLM_CONFIGS', {})

    @property
    def defaults(self) -> Dict[str, str]:
        """获取默认配置"""
        return self._load_model_config().get('defaults', {})

    def list_models(self) -> List[str]:
        """
        列出所有可用的模型名称

        Returns:
            List[str]: 模型名称列表
        """
        return list(self.model_config.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """
        获取模型配置信息

        Args:
            model_name: 模型名称

        Returns:
            Dict[str, Any]: 模型配置信息

        Raises:
            ModelNotFoundException: 模型未找到
        """
        if model_name not in self.model_config:
            raise ModelNotFoundException(model_name)

        return self.model_config[model_name]
    
    def get_model_client(self, model_name: str) -> ChatCompletionClient:
        """
        获取模型客户端，使用AutoGen的load_component方法

        Args:
            model_name: 模型名称

        Returns:
            ChatCompletionClient: 模型客户端实例

        Raises:
            ModelNotFoundException: 模型未找到
            ModelException: 客户端创建失败
        """
        # 检查缓存
        if model_name in self._client_cache:
            return self._client_cache[model_name]

        # 获取模型配置
        model_config = self.get_model_info(model_name)

        try:
            # 使用AutoGen的load_component方法创建客户端
            model_client = ChatCompletionClient.load_component(model_config)
            if not model_client:
                raise ValueError(f"Failed to load model client for '{model_name}'")

            # 缓存客户端
            self._client_cache[model_name] = model_client

            logger.info(f"Created model client for {model_name}")
            return model_client

        except Exception as e:
            raise ModelException(
                f"Failed to create model client for {model_name}: {e}",
                model_alias=model_name,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR,
                cause=e
            )
    
    def get_default_model(self, model_type: str = "primary_model") -> str:
        """
        获取默认模型名称

        Args:
            model_type: 模型类型 (primary_model, fallback_model, local_model, etc.)

        Returns:
            str: 模型名称
        """
        return self.defaults.get(model_type, "gpt-4o-mini")

    def has_model(self, model_name: str) -> bool:
        """
        检查模型是否存在

        Args:
            model_name: 模型名称

        Returns:
            bool: 模型是否存在
        """
        return model_name in self.model_config

    def get_model_capabilities(self, model_name: str) -> Dict[str, bool]:
        """
        获取模型能力

        Args:
            model_name: 模型名称

        Returns:
            Dict[str, bool]: 模型能力字典

        Raises:
            ModelNotFoundException: 模型未找到
        """
        model_info = self.get_model_info(model_name)
        return model_info.get('config', {}).get('model_capabilities', {})

    def supports_capability(self, model_name: str, capability: str) -> bool:
        """
        检查模型是否支持特定能力

        Args:
            model_name: 模型名称
            capability: 能力名称 (vision, function_calling, json_output)

        Returns:
            bool: 是否支持该能力
        """
        try:
            capabilities = self.get_model_capabilities(model_name)
            return capabilities.get(capability, False)
        except ModelNotFoundException:
            return False

    def filter_models_by_capability(self, capability: str) -> List[str]:
        """
        根据能力过滤模型

        Args:
            capability: 能力名称

        Returns:
            List[str]: 支持该能力的模型列表
        """
        return [
            model_name for model_name in self.list_models()
            if self.supports_capability(model_name, capability)
        ]
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("Reloading model configuration...")

        # 清除缓存
        self._config_cache = None
        self._client_cache.clear()

        # 重新加载配置
        self._load_model_config()

        logger.info("Model configuration reloaded")

    def validate_model(self, model_name: str) -> bool:
        """
        验证模型配置

        Args:
            model_name: 模型名称

        Returns:
            bool: 验证结果
        """
        try:
            # 检查模型是否存在
            if not self.has_model(model_name):
                logger.error(f"Model {model_name} not found in configuration")
                return False

            # 尝试创建客户端（不缓存）
            model_config = self.get_model_info(model_name)
            test_client = ChatCompletionClient.load_component(model_config)

            if test_client:
                logger.info(f"Model {model_name} validation passed")
                return True
            else:
                logger.error(f"Failed to create client for model {model_name}")
                return False

        except Exception as e:
            logger.error(f"Model {model_name} validation failed: {e}")
            return False


# 全局模型管理器实例
_model_manager: Optional[ModelManager] = None


def get_model_manager(config_path: Optional[Path] = None) -> ModelManager:
    """
    获取全局模型管理器实例

    Args:
        config_path: 配置文件路径

    Returns:
        ModelManager: 模型管理器实例
    """
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager(config_path)
    return _model_manager
