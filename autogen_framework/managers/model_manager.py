"""
AutoGen Multi-Agent Framework - Model Manager

This module manages model configurations and provides unified access to different model providers.
Supports OpenAI, Azure OpenAI, Anthropic, Ollama and other providers through AutoGen extensions.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
import asyncio
from functools import lru_cache

from infrastructure.exceptions import (
    ModelException, 
    ModelNotFoundException, 
    ConfigurationException,
    ErrorCode
)
from infrastructure.logging_config import get_framework_logger

logger = get_framework_logger("model_manager")


@dataclass
class ModelCapabilities:
    """模型能力定义"""
    text_generation: bool = True
    function_calling: bool = False
    vision: bool = False
    json_mode: bool = False
    code_generation: bool = False
    reasoning: bool = False


@dataclass
class ModelCost:
    """模型成本信息"""
    input_cost_per_1k: float = 0.0
    output_cost_per_1k: float = 0.0


@dataclass
class ModelInfo:
    """模型信息"""
    alias: str
    provider: str
    model_name: str
    config: Dict[str, Any]
    capabilities: ModelCapabilities
    cost: ModelCost
    context_window: int = 4096
    
    def supports_capability(self, capability: str) -> bool:
        """检查模型是否支持特定能力"""
        return getattr(self.capabilities, capability, False)


class ModelManager:
    """模型管理器"""
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        初始化模型管理器
        
        Args:
            config_path: 模型配置文件路径
        """
        self.config_path = Path(config_path) if config_path else Path("config/model_config.yaml")
        self._models: Dict[str, ModelInfo] = {}
        self._clients: Dict[str, Any] = {}
        self._providers: Dict[str, Dict[str, Any]] = {}
        self._defaults: Dict[str, str] = {}
        
        # 加载配置
        self._load_config()
        
        logger.info(f"Model manager initialized with {len(self._models)} models")
    
    def _load_config(self):
        """加载模型配置"""
        try:
            if not self.config_path.exists():
                raise ConfigurationException(
                    f"Model config file not found: {self.config_path}",
                    config_path=str(self.config_path)
                )
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 加载模型配置
            models_config = config.get('models', {})
            for alias, model_config in models_config.items():
                self._load_model_config(alias, model_config)
            
            # 加载提供商配置
            self._providers = config.get('providers', {})
            
            # 加载默认配置
            self._defaults = config.get('defaults', {})
            
            logger.info(f"Loaded {len(self._models)} model configurations")
            
        except Exception as e:
            raise ConfigurationException(f"Failed to load model config: {e}", cause=e)
    
    def _load_model_config(self, alias: str, config: Dict[str, Any]):
        """加载单个模型配置"""
        try:
            # 解析能力
            capabilities_list = config.get('capabilities', [])
            capabilities = ModelCapabilities()
            for cap in capabilities_list:
                if hasattr(capabilities, cap):
                    setattr(capabilities, cap, True)
            
            # 解析成本
            cost_config = config.get('cost_per_1k_tokens', {})
            cost = ModelCost(
                input_cost_per_1k=cost_config.get('input', 0.0),
                output_cost_per_1k=cost_config.get('output', 0.0)
            )
            
            # 处理环境变量替换
            model_config = self._resolve_env_vars(config.get('config', {}))
            
            # 创建模型信息
            model_info = ModelInfo(
                alias=alias,
                provider=config['provider'],
                model_name=config['model_name'],
                config=model_config,
                capabilities=capabilities,
                cost=cost,
                context_window=config.get('context_window', 4096)
            )
            
            self._models[alias] = model_info
            logger.debug(f"Loaded model config: {alias} ({model_info.provider})")
            
        except Exception as e:
            logger.warning(f"Failed to load model config for {alias}: {e}")
    
    def _resolve_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """解析配置中的环境变量"""
        resolved_config = {}
        
        for key, value in config.items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # 环境变量格式: ${VAR_NAME}
                env_var = value[2:-1]
                env_value = os.getenv(env_var)
                if env_value is not None:
                    resolved_config[key] = env_value
                else:
                    logger.warning(f"Environment variable {env_var} not found for {key}")
                    # 保留原始值，让后续处理决定是否报错
                    resolved_config[key] = value
            else:
                resolved_config[key] = value
        
        return resolved_config
    
    def get_model_info(self, alias: str) -> ModelInfo:
        """
        获取模型信息
        
        Args:
            alias: 模型别名
            
        Returns:
            ModelInfo: 模型信息
            
        Raises:
            ModelNotFoundException: 模型未找到
        """
        if alias not in self._models:
            raise ModelNotFoundException(alias)
        
        return self._models[alias]
    
    def list_models(self, provider: Optional[str] = None, capability: Optional[str] = None) -> List[ModelInfo]:
        """
        列出模型
        
        Args:
            provider: 过滤提供商
            capability: 过滤能力
            
        Returns:
            List[ModelInfo]: 模型信息列表
        """
        models = list(self._models.values())
        
        if provider:
            models = [m for m in models if m.provider == provider]
        
        if capability:
            models = [m for m in models if m.supports_capability(capability)]
        
        return models
    
    def get_model_client(self, alias: str) -> Any:
        """
        获取模型客户端
        
        Args:
            alias: 模型别名
            
        Returns:
            模型客户端实例
            
        Raises:
            ModelNotFoundException: 模型未找到
            ModelException: 客户端创建失败
        """
        # 检查缓存
        if alias in self._clients:
            return self._clients[alias]
        
        # 获取模型信息
        model_info = self.get_model_info(alias)
        
        try:
            # 创建客户端
            client = self._create_model_client(model_info)
            
            # 缓存客户端
            self._clients[alias] = client
            
            logger.info(f"Created model client for {alias}")
            return client
            
        except Exception as e:
            raise ModelException(
                f"Failed to create model client for {alias}: {e}",
                model_alias=alias,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR,
                cause=e
            )
    
    def _create_model_client(self, model_info: ModelInfo) -> Any:
        """创建模型客户端"""
        provider_config = self._providers.get(model_info.provider)
        if not provider_config:
            raise ModelException(
                f"Unknown provider: {model_info.provider}",
                model_alias=model_info.alias,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR
            )
        
        # 检查必需的环境变量
        required_vars = provider_config.get('required_env_vars', [])
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ModelException(
                f"Missing required environment variables for {model_info.provider}: {missing_vars}",
                model_alias=model_info.alias,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR
            )
        
        # 动态导入客户端类
        client_class_path = provider_config['client_class']
        try:
            module_path, class_name = client_class_path.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            client_class = getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            raise ModelException(
                f"Failed to import client class {client_class_path}: {e}",
                model_alias=model_info.alias,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR,
                cause=e
            )
        
        # 创建客户端实例
        try:
            client = client_class(
                model=model_info.model_name,
                **model_info.config
            )
            return client
        except Exception as e:
            raise ModelException(
                f"Failed to create client instance: {e}",
                model_alias=model_info.alias,
                error_code=ErrorCode.MODEL_CONFIGURATION_ERROR,
                cause=e
            )
    
    def get_default_model(self, model_type: str = "primary_model") -> str:
        """
        获取默认模型别名
        
        Args:
            model_type: 模型类型 (primary_model, fallback_model, local_model)
            
        Returns:
            str: 模型别名
        """
        return self._defaults.get(model_type, "gpt-4o-mini")
    
    def validate_model(self, alias: str) -> bool:
        """
        验证模型配置
        
        Args:
            alias: 模型别名
            
        Returns:
            bool: 验证结果
        """
        try:
            model_info = self.get_model_info(alias)
            
            # 检查提供商配置
            provider_config = self._providers.get(model_info.provider)
            if not provider_config:
                logger.error(f"Unknown provider for model {alias}: {model_info.provider}")
                return False
            
            # 检查必需的环境变量
            required_vars = provider_config.get('required_env_vars', [])
            missing_vars = [var for var in required_vars if not os.getenv(var)]
            if missing_vars:
                logger.error(f"Missing environment variables for {alias}: {missing_vars}")
                return False
            
            logger.info(f"Model {alias} validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Model {alias} validation failed: {e}")
            return False
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("Reloading model configuration...")
        
        # 清除缓存
        self._models.clear()
        self._clients.clear()
        self._providers.clear()
        self._defaults.clear()
        
        # 重新加载
        self._load_config()
        
        logger.info("Model configuration reloaded")


# 全局模型管理器实例
_model_manager: Optional[ModelManager] = None


def get_model_manager(config_path: Optional[Union[str, Path]] = None) -> ModelManager:
    """
    获取全局模型管理器实例
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        ModelManager: 模型管理器实例
    """
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager(config_path)
    return _model_manager
