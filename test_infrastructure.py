#!/usr/bin/env python3
"""
测试脚本：验证基础设施层的实现
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "autogen_framework"))

def test_exceptions():
    """测试异常处理模块"""
    print("🧪 测试异常处理模块...")
    
    try:
        from infrastructure.exceptions import (
            AutoGenFrameworkException, 
            ErrorCode,
            AgentNotFoundException,
            ValidationException,
            create_error_response
        )
        
        # 测试基础异常
        exc = AutoGenFrameworkException("Test error", ErrorCode.UNKNOWN_ERROR)
        print(f"✅ 基础异常创建成功: {exc}")
        
        # 测试异常转换为字典
        exc_dict = exc.to_dict()
        print(f"✅ 异常字典转换: {exc_dict}")
        
        # 测试特定异常
        agent_exc = AgentNotFoundException("test_agent")
        print(f"✅ Agent异常创建成功: {agent_exc}")
        
        # 测试错误响应创建
        error_response = create_error_response(agent_exc)
        print(f"✅ 错误响应创建: {error_response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常处理模块测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理模块"""
    print("🧪 测试配置管理模块...")
    
    try:
        from infrastructure.config_manager import (
            ConfigManager,
            get_config_manager,
            get_config,
            AppConfig,
            LoggingConfig,
            APIConfig
        )
        
        # 测试配置管理器创建
        config_manager = get_config_manager()
        print(f"✅ 配置管理器创建成功: {config_manager}")
        
        # 测试配置获取
        config = get_config()
        print(f"✅ 配置获取成功: {config.app.name}")
        print(f"   - 应用名称: {config.app.name}")
        print(f"   - 版本: {config.app.version}")
        print(f"   - 环境: {config.app.environment}")
        print(f"   - 调试模式: {config.app.debug}")
        print(f"   - 主机: {config.app.host}")
        print(f"   - 端口: {config.app.port}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理模块测试失败: {e}")
        return False

def test_logging_config():
    """测试日志配置模块"""
    print("🧪 测试日志配置模块...")
    
    try:
        from infrastructure.logging_config import (
            setup_logging,
            get_framework_logger,
            get_trace_logger,
            get_event_logger,
            log_framework_event
        )
        
        # 测试日志设置
        logging_config = setup_logging()
        print(f"✅ 日志配置成功: {logging_config}")
        
        # 测试获取不同类型的日志器
        framework_logger = get_framework_logger("test")
        trace_logger = get_trace_logger("test")
        event_logger = get_event_logger("test")
        
        print(f"✅ 框架日志器: {framework_logger.name}")
        print(f"✅ Trace日志器: {trace_logger.name}")
        print(f"✅ Event日志器: {event_logger.name}")
        
        # 测试日志记录
        framework_logger.info("测试框架日志")
        trace_logger.debug("测试trace日志")
        event_logger.info("测试event日志")
        
        # 测试便捷函数
        log_framework_event("测试框架事件", "INFO", module="test")
        
        print("✅ 日志记录测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志配置模块测试失败: {e}")
        return False

def test_main_app():
    """测试主应用"""
    print("🧪 测试主应用...")
    
    try:
        # 测试应用创建
        from main import create_app, APP_NAME, APP_VERSION
        
        app = create_app()
        print(f"✅ FastAPI应用创建成功: {APP_NAME} v{APP_VERSION}")
        print(f"   - 应用标题: {app.title}")
        print(f"   - 应用版本: {app.version}")
        
        # 测试路由
        routes = [route.path for route in app.routes]
        print(f"✅ 应用路由: {routes}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 测试AutoGen框架基础设施层...")
    print("=" * 50)
    
    tests = [
        ("异常处理", test_exceptions),
        ("配置管理", test_config_manager),
        ("日志配置", test_logging_config),
        ("主应用", test_main_app),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础设施层测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())
