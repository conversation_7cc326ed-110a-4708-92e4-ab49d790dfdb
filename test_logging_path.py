#!/usr/bin/env python3
"""
测试脚本：验证日志路径配置
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "autogen_framework"))

def test_logging_path():
    """测试日志路径配置"""
    print("🧪 测试日志路径配置...")
    
    try:
        from infrastructure.logging_config import setup_logging
        from infrastructure.config_manager import get_config
        
        # 清理可能存在的测试日志文件
        test_log_path = Path("autogen_framework/logs/test.log")
        if test_log_path.exists():
            test_log_path.unlink()
        
        # 获取配置
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"   日志文件配置: {config.logging.file}")
        
        # 设置测试日志配置
        logging_config = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_logging': {
                'enabled': True,
                'filename': 'test.log'
            }
        }
        
        setup_logging(logging_config)
        print("✅ 日志系统初始化成功")
        
        # 测试日志记录
        from infrastructure.logging_config import get_framework_logger
        logger = get_framework_logger("test")
        logger.info("这是一条测试日志消息")
        
        # 检查日志文件是否在正确位置创建
        expected_log_path = Path("autogen_framework/logs/test.log")
        if expected_log_path.exists():
            print(f"✅ 日志文件创建在正确位置: {expected_log_path}")
            
            # 检查日志内容
            with open(expected_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "这是一条测试日志消息" in content:
                    print("✅ 日志内容写入正确")
                else:
                    print("❌ 日志内容写入失败")
                    return False
        else:
            print(f"❌ 日志文件未在预期位置创建: {expected_log_path}")
            return False
        
        # 检查日志目录结构
        logs_dir = Path("autogen_framework/logs")
        if logs_dir.exists():
            log_files = list(logs_dir.glob("*.log"))
            print(f"✅ 日志目录存在，包含 {len(log_files)} 个日志文件:")
            for log_file in log_files:
                size = log_file.stat().st_size
                print(f"   - {log_file.name}: {size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志路径测试失败: {e}")
        return False

def test_main_app_logging():
    """测试主应用日志"""
    print("🧪 测试主应用日志...")
    
    try:
        # 检查主应用日志文件
        app_log_path = Path("autogen_framework/logs/app.log")
        
        if app_log_path.exists():
            print(f"✅ 主应用日志文件存在: {app_log_path}")
            
            # 检查文件大小
            size = app_log_path.stat().st_size
            print(f"   文件大小: {size} bytes")
            
            # 检查最近的日志内容
            with open(app_log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"   总行数: {len(lines)}")
                    print(f"   最后一行: {lines[-1].strip()}")
                else:
                    print("   文件为空")
        else:
            print(f"⚠️ 主应用日志文件不存在: {app_log_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用日志测试失败: {e}")
        return False

def test_log_directory_structure():
    """测试日志目录结构"""
    print("🧪 测试日志目录结构...")
    
    try:
        logs_dir = Path("autogen_framework/logs")
        
        if not logs_dir.exists():
            print(f"❌ 日志目录不存在: {logs_dir}")
            return False
        
        print(f"✅ 日志目录存在: {logs_dir}")
        
        # 检查目录权限
        if logs_dir.is_dir():
            print("✅ 是有效的目录")
        else:
            print("❌ 不是有效的目录")
            return False
        
        # 列出所有日志文件
        log_files = list(logs_dir.glob("*.log"))
        print(f"✅ 找到 {len(log_files)} 个日志文件:")
        
        for log_file in log_files:
            stat = log_file.stat()
            print(f"   - {log_file.name}")
            print(f"     大小: {stat.st_size} bytes")
            print(f"     修改时间: {stat.st_mtime}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志目录结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 测试AutoGen框架日志路径配置...")
    print("=" * 50)
    
    tests = [
        ("日志路径配置", test_logging_path),
        ("主应用日志", test_main_app_logging),
        ("日志目录结构", test_log_directory_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有日志路径测试通过！")
        print("📁 日志文件位置: autogen_framework/logs/")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
